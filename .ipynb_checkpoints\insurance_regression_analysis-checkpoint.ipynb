{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Comprehensive Insurance Cost Regression Analysis\n", "\n", "This notebook performs a comprehensive regression analysis on an insurance dataset to predict insurance costs based on various demographic and health factors.\n", "\n", "## Dataset Overview\n", "The dataset contains information about insurance policyholders including:\n", "- **Age**: Age of the policyholder\n", "- **BMI**: Body Mass Index\n", "- **Smoker**: Whether the person is a smoker (yes/no)\n", "- **Number_of_Children**: Number of children/dependents\n", "- **Gender**: Gender of the policyholder (male/female)\n", "- **Region**: Geographic region (northeast, northwest, southeast, southwest)\n", "- **Insurance_Cost**: Annual insurance cost (target variable)\n", "\n", "## Analysis Goals\n", "1. Explore and understand the dataset\n", "2. Perform data preprocessing\n", "3. Implement multiple regression models\n", "4. Compare model performances\n", "5. Provide insights and recommendations"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Required Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data manipulation and analysis\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# Data visualization\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "\n", "# Machine learning\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder, PolynomialFeatures\n", "from sklearn.linear_model import LinearRegression, Ridge, Lasso\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "\n", "# Warnings\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style for plots\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# Display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON> and Explore the Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the dataset\n", "df = pd.read_csv('Insurance_dataset.csv')\n", "\n", "print(\"Dataset loaded successfully!\")\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(\"\\nFirst 5 rows:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic information about the dataset\n", "print(\"Dataset Info:\")\n", "print(df.info())\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"\\nBasic Statistics:\")\n", "df.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for missing values\n", "print(\"Missing Values:\")\n", "missing_values = df.isnull().sum()\n", "print(missing_values)\n", "print(f\"\\nTotal missing values: {missing_values.sum()}\")\n", "\n", "# Check data types\n", "print(\"\\nData Types:\")\n", "print(df.dtypes)\n", "\n", "# Check unique values for categorical columns\n", "categorical_cols = ['Smoker', 'Gender', 'Region']\n", "print(\"\\nUnique values in categorical columns:\")\n", "for col in categorical_cols:\n", "    print(f\"{col}: {df[col].unique()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Visualization and Exploratory Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a comprehensive visualization of the dataset\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "fig.suptitle('Insurance Dataset - Exploratory Data Analysis', fontsize=16, fontweight='bold')\n", "\n", "# Distribution of Insurance Cost (target variable)\n", "axes[0, 0].hist(df['Insurance_Cost'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "axes[0, 0].set_title('Distribution of Insurance Cost')\n", "axes[0, 0].set_xlabel('Insurance Cost ($)')\n", "axes[0, 0].set_ylabel('Frequency')\n", "\n", "# Age distribution\n", "axes[0, 1].hist(df['Age'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')\n", "axes[0, 1].set_title('Distribution of Age')\n", "axes[0, 1].set_xlabel('Age')\n", "axes[0, 1].set_ylabel('Frequency')\n", "\n", "# BMI distribution\n", "axes[0, 2].hist(df['BMI'], bins=20, alpha=0.7, color='salmon', edgecolor='black')\n", "axes[0, 2].set_title('Distribution of BMI')\n", "axes[0, 2].set_xlabel('BMI')\n", "axes[0, 2].set_ylabel('Frequency')\n", "\n", "# Smoker vs Insurance Cost\n", "sns.boxplot(data=df, x='Smoker', y='Insurance_Cost', ax=axes[1, 0])\n", "axes[1, 0].set_title('Insurance Cost by Smoking Status')\n", "axes[1, 0].set_ylabel('Insurance Cost ($)')\n", "\n", "# Gender vs Insurance Cost\n", "sns.boxplot(data=df, x='Gender', y='Insurance_Cost', ax=axes[1, 1])\n", "axes[1, 1].set_title('Insurance Cost by Gender')\n", "axes[1, 1].set_ylabel('Insurance Cost ($)')\n", "\n", "# Region vs Insurance Cost\n", "sns.boxplot(data=df, x='Region', y='Insurance_Cost', ax=axes[1, 2])\n", "axes[1, 2].set_title('Insurance Cost by Region')\n", "axes[1, 2].set_ylabel('Insurance Cost ($)')\n", "axes[1, 2].tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Correlation analysis\n", "# First, let's create a copy of the dataframe for correlation analysis\n", "df_corr = df.copy()\n", "\n", "# Encode categorical variables for correlation analysis\n", "le_smoker = LabelEncoder()\n", "le_gender = LabelEncoder()\n", "le_region = LabelEncoder()\n", "\n", "df_corr['Smoker_encoded'] = le_smoker.fit_transform(df_corr['Smoker'])\n", "df_corr['Gender_encoded'] = le_gender.fit_transform(df_corr['Gender'])\n", "df_corr['Region_encoded'] = le_region.fit_transform(df_corr['Region'])\n", "\n", "# Select numerical columns for correlation\n", "numerical_cols = ['Age', 'BMI', 'Number_of_Children', 'Smoker_encoded', 'Gender_encoded', 'Region_encoded', 'Insurance_Cost']\n", "correlation_matrix = df_corr[numerical_cols].corr()\n", "\n", "# Create correlation heatmap\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, \n", "            square=True, linewidths=0.5, cbar_kws={\"shrink\": .8})\n", "plt.title('Correlation Matrix of Features', fontsize=14, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Correlation with Insurance Cost:\")\n", "correlations = correlation_matrix['Insurance_Cost'].sort_values(ascending=False)\n", "for feature, corr in correlations.items():\n", "    if feature != 'Insurance_Cost':\n", "        print(f\"{feature}: {corr:.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sc<PERSON><PERSON> plots to visualize relationships\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "fig.suptitle('Relationships between Features and Insurance Cost', fontsize=16, fontweight='bold')\n", "\n", "# Age vs Insurance Cost\n", "axes[0, 0].scatter(df['Age'], df['Insurance_Cost'], alpha=0.6, color='blue')\n", "axes[0, 0].set_xlabel('Age')\n", "axes[0, 0].set_ylabel('Insurance Cost ($)')\n", "axes[0, 0].set_title('Age vs Insurance Cost')\n", "\n", "# BMI vs Insurance Cost\n", "axes[0, 1].scatter(df['BMI'], df['Insurance_Cost'], alpha=0.6, color='green')\n", "axes[0, 1].set_xlabel('BMI')\n", "axes[0, 1].set_ylabel('Insurance Cost ($)')\n", "axes[0, 1].set_title('BMI vs Insurance Cost')\n", "\n", "# Number of Children vs Insurance Cost\n", "axes[1, 0].scatter(df['Number_of_Children'], df['Insurance_Cost'], alpha=0.6, color='red')\n", "axes[1, 0].set_xlabel('Number of Children')\n", "axes[1, 0].set_ylabel('Insurance Cost ($)')\n", "axes[1, 0].set_title('Number of Children vs Insurance Cost')\n", "\n", "# Age vs BMI colored by Smoker status\n", "smokers = df[df['Smoker'] == 'yes']\n", "non_smokers = df[df['Smoker'] == 'no']\n", "axes[1, 1].scatter(non_smokers['Age'], non_smokers['BMI'], alpha=0.6, color='blue', label='Non-smoker')\n", "axes[1, 1].scatter(smokers['Age'], smokers['BMI'], alpha=0.6, color='red', label='Smoker')\n", "axes[1, 1].set_xlabel('Age')\n", "axes[1, 1].set_ylabel('BMI')\n", "axes[1, 1].set_title('Age vs BMI by Smoking Status')\n", "axes[1, 1].legend()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a copy of the dataset for preprocessing\n", "df_processed = df.copy()\n", "\n", "print(\"Original dataset shape:\", df_processed.shape)\n", "print(\"\\nChecking for missing values:\")\n", "print(df_processed.isnull().sum())\n", "\n", "# Since there are no missing values, we'll proceed with encoding categorical variables\n", "print(\"\\nNo missing values found. Proceeding with categorical encoding...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Encode categorical variables using one-hot encoding\n", "# This is better than label encoding for categorical variables with no ordinal relationship\n", "\n", "# One-hot encode categorical variables\n", "df_encoded = pd.get_dummies(df_processed, columns=['Smoker', 'Gender', 'Region'], drop_first=True)\n", "\n", "print(\"Dataset after one-hot encoding:\")\n", "print(f\"Shape: {df_encoded.shape}\")\n", "print(\"\\nColumns:\")\n", "print(df_encoded.columns.tolist())\n", "print(\"\\nFirst 5 rows:\")\n", "df_encoded.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare features and target variable\n", "X = df_encoded.drop('Insurance_Cost', axis=1)\n", "y = df_encoded['Insurance_Cost']\n", "\n", "print(\"Features (X):\")\n", "print(f\"Shape: {X.shape}\")\n", "print(f\"Columns: {X.columns.tolist()}\")\n", "\n", "print(\"\\nTarget variable (y):\")\n", "print(f\"Shape: {y.shape}\")\n", "print(f\"Statistics: Mean={y.mean():.2f}, Std={y.std():.2f}, Min={y.min():.2f}, Max={y.max():.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Split the data into training and testing sets\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=None)\n", "\n", "print(\"Data split completed:\")\n", "print(f\"Training set: X_train {X_train.shape}, y_train {y_train.shape}\")\n", "print(f\"Testing set: X_test {X_test.shape}, y_test {y_test.shape}\")\n", "\n", "# Feature scaling (important for regularized models)\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(\"\\nFeature scaling completed.\")\n", "print(f\"Scaled training features shape: {X_train_scaled.shape}\")\n", "print(f\"Scaled testing features shape: {X_test_scaled.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Model Implementation and Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize models\n", "models = {\n", "    'Linear Regression': LinearRegression(),\n", "    'Ridge Regression': Ridge(alpha=1.0, random_state=42),\n", "    'Lasso Regression': <PERSON><PERSON>(alpha=1.0, random_state=42),\n", "    'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42)\n", "}\n", "\n", "# Dictionary to store model results\n", "model_results = {}\n", "\n", "print(\"Models initialized:\")\n", "for name in models.keys():\n", "    print(f\"- {name}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train models and make predictions\n", "for name, model in models.items():\n", "    print(f\"\\nTraining {name}...\")\n", "    \n", "    # Use scaled data for linear models, original data for tree-based models\n", "    if name in ['Ridge Regression', 'Lasso Regression']:\n", "        model.fit(X_train_scaled, y_train)\n", "        y_pred = model.predict(X_test_scaled)\n", "        train_pred = model.predict(X_train_scaled)\n", "    else:\n", "        model.fit(X_train, y_train)\n", "        y_pred = model.predict(X_test)\n", "        train_pred = model.predict(X_train)\n", "    \n", "    # Calculate metrics\n", "    r2_test = r2_score(y_test, y_pred)\n", "    r2_train = r2_score(y_train, train_pred)\n", "    rmse = np.sqrt(mean_squared_error(y_test, y_pred))\n", "    mae = mean_absolute_error(y_test, y_pred)\n", "    \n", "    # Store results\n", "    model_results[name] = {\n", "        'model': model,\n", "        'y_pred': y_pred,\n", "        'train_pred': train_pred,\n", "        'r2_test': r2_test,\n", "        'r2_train': r2_train,\n", "        'rmse': rmse,\n", "        'mae': mae\n", "    }\n", "    \n", "    print(f\"  R² (Test): {r2_test:.4f}\")\n", "    print(f\"  R² (Train): {r2_train:.4f}\")\n", "    print(f\"  RMSE: {rmse:.2f}\")\n", "    print(f\"  MAE: {mae:.2f}\")\n", "\n", "print(\"\\nAll models trained successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Model Performance Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a comparison table of model performances\n", "comparison_df = pd.DataFrame({\n", "    'Model': list(model_results.keys()),\n", "    'R² (Test)': [results['r2_test'] for results in model_results.values()],\n", "    'R² (Train)': [results['r2_train'] for results in model_results.values()],\n", "    'RMSE': [results['rmse'] for results in model_results.values()],\n", "    'MAE': [results['mae'] for results in model_results.values()]\n", "})\n", "\n", "# Sort by R² (Test) in descending order\n", "comparison_df = comparison_df.sort_values('R² (Test)', ascending=False)\n", "\n", "print(\"Model Performance Comparison:\")\n", "print(\"=\" * 80)\n", "print(comparison_df.to_string(index=False, float_format='%.4f'))\n", "\n", "# Calculate overfitting indicator (difference between train and test R²)\n", "comparison_df['Overfitting'] = comparison_df['R² (Train)'] - comparison_df['R² (Test)']\n", "print(\"\\nOverfitting Analysis (Train R² - Test R²):\")\n", "print(\"=\" * 50)\n", "for idx, row in comparison_df.iterrows():\n", "    print(f\"{row['Model']}: {row['Overfitting']:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Model Visualization and Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create actual vs predicted plots for all models\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "fig.suptitle('Actual vs Predicted Values - All Models', fontsize=16, fontweight='bold')\n", "\n", "axes = axes.ravel()\n", "\n", "for i, (name, results) in enumerate(model_results.items()):\n", "    ax = axes[i]\n", "    \n", "    # Plot actual vs predicted\n", "    ax.scatter(y_test, results['y_pred'], alpha=0.6, color='blue')\n", "    \n", "    # Plot perfect prediction line\n", "    min_val = min(y_test.min(), results['y_pred'].min())\n", "    max_val = max(y_test.max(), results['y_pred'].max())\n", "    ax.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='Perfect Prediction')\n", "    \n", "    ax.set_xlabel('Actual Insurance Cost ($)')\n", "    ax.set_ylabel('Predicted Insurance Cost ($)')\n", "    ax.set_title(f'{name}\\nR² = {results[\"r2_test\"]:.4f}, RMSE = {results[\"rmse\"]:.2f}')\n", "    ax.legend()\n", "    ax.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create residual plots for all models\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "fig.suptitle('Residual Plots - All Models', fontsize=16, fontweight='bold')\n", "\n", "axes = axes.ravel()\n", "\n", "for i, (name, results) in enumerate(model_results.items()):\n", "    ax = axes[i]\n", "    \n", "    # Calculate residuals\n", "    residuals = y_test - results['y_pred']\n", "    \n", "    # Plot residuals vs predicted values\n", "    ax.scatter(results['y_pred'], residuals, alpha=0.6, color='green')\n", "    ax.axhline(y=0, color='red', linestyle='--', lw=2)\n", "    \n", "    ax.set_xlabel('Predicted Insurance Cost ($)')\n", "    ax.set_ylabel('Residuals ($)')\n", "    ax.set_title(f'{name} - Residuals')\n", "    ax.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature importance analysis (for Random Forest)\n", "rf_model = model_results['Random Forest']['model']\n", "feature_importance = pd.DataFrame({\n", "    'Feature': X.columns,\n", "    'Importance': rf_model.feature_importances_\n", "}).sort_values('Importance', ascending=False)\n", "\n", "plt.figure(figsize=(10, 6))\n", "sns.barplot(data=feature_importance, x='Importance', y='Feature', palette='viridis')\n", "plt.title('Feature Importance - Random Forest Model', fontsize=14, fontweight='bold')\n", "plt.xlabel('Importance Score')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Feature Importance (Random Forest):\")\n", "print(\"=\" * 40)\n", "for idx, row in feature_importance.iterrows():\n", "    print(f\"{row['Feature']}: {row['Importance']:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Advanced Analysis - Polynomial Regression"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Implement polynomial regression for comparison\n", "poly_features = PolynomialFeatures(degree=2, include_bias=False)\n", "X_train_poly = poly_features.fit_transform(X_train)\n", "X_test_poly = poly_features.transform(X_test)\n", "\n", "# Train polynomial regression\n", "poly_model = LinearRegression()\n", "poly_model.fit(X_train_poly, y_train)\n", "\n", "# Make predictions\n", "y_pred_poly = poly_model.predict(X_test_poly)\n", "y_train_pred_poly = poly_model.predict(X_train_poly)\n", "\n", "# Calculate metrics\n", "r2_test_poly = r2_score(y_test, y_pred_poly)\n", "r2_train_poly = r2_score(y_train, y_train_pred_poly)\n", "rmse_poly = np.sqrt(mean_squared_error(y_test, y_pred_poly))\n", "mae_poly = mean_absolute_error(y_test, y_pred_poly)\n", "\n", "print(\"Polynomial Regression (Degree 2) Results:\")\n", "print(f\"R² (Test): {r2_test_poly:.4f}\")\n", "print(f\"R² (Train): {r2_train_poly:.4f}\")\n", "print(f\"RMSE: {rmse_poly:.2f}\")\n", "print(f\"MAE: {mae_poly:.2f}\")\n", "print(f\"Overfitting: {r2_train_poly - r2_test_poly:.4f}\")\n", "\n", "# Add to model results for comparison\n", "model_results['Polynomial Regression'] = {\n", "    'model': poly_model,\n", "    'y_pred': y_pred_poly,\n", "    'train_pred': y_train_pred_poly,\n", "    'r2_test': r2_test_poly,\n", "    'r2_train': r2_train_poly,\n", "    'rmse': rmse_poly,\n", "    'mae': mae_poly\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Final Model Comparison and Insights"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Updated comparison including polynomial regression\n", "final_comparison = pd.DataFrame({\n", "    'Model': list(model_results.keys()),\n", "    'R² (Test)': [results['r2_test'] for results in model_results.values()],\n", "    'R² (Train)': [results['r2_train'] for results in model_results.values()],\n", "    'RMSE': [results['rmse'] for results in model_results.values()],\n", "    'MAE': [results['mae'] for results in model_results.values()]\n", "})\n", "\n", "final_comparison['Overfitting'] = final_comparison['R² (Train)'] - final_comparison['R² (Test)']\n", "final_comparison = final_comparison.sort_values('R² (Test)', ascending=False)\n", "\n", "print(\"FINAL MODEL PERFORMANCE COMPARISON:\")\n", "print(\"=\" * 90)\n", "print(final_comparison.to_string(index=False, float_format='%.4f'))\n", "\n", "# Visualize model comparison\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))\n", "\n", "# R² comparison\n", "x_pos = np.arange(len(final_comparison))\n", "ax1.bar(x_pos - 0.2, final_comparison['R² (Train)'], 0.4, label='Train R²', alpha=0.8)\n", "ax1.bar(x_pos + 0.2, final_comparison['R² (Test)'], 0.4, label='Test R²', alpha=0.8)\n", "ax1.set_xlabel('Models')\n", "ax1.set_ylabel('R² Score')\n", "ax1.set_title('R² Score Comparison')\n", "ax1.set_xticks(x_pos)\n", "ax1.set_xticklabels(final_comparison['Model'], rotation=45)\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# RMSE comparison\n", "ax2.bar(final_comparison['Model'], final_comparison['RMSE'], alpha=0.8, color='coral')\n", "ax2.set_xlabel('Models')\n", "ax2.set_ylabel('RMSE ($)')\n", "ax2.set_title('RMSE Comparison')\n", "ax2.tick_params(axis='x', rotation=45)\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Key Insights and Conclusions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Model Performance Summary\n", "\n", "Based on our comprehensive regression analysis of the insurance dataset, here are the key findings:\n", "\n", "#### 1. **Best Performing Model**\n", "- The model with the highest R² score on the test set provides the best predictive performance\n", "- Random Forest typically performs well due to its ability to capture non-linear relationships\n", "- Linear models provide good baseline performance and interpretability\n", "\n", "#### 2. **Feature Importance Insights**\n", "- **Smoking Status**: Likely the most important predictor of insurance costs\n", "- **Age**: Generally shows positive correlation with insurance costs\n", "- **BMI**: Higher BMI typically associated with higher costs\n", "- **Number of Children**: May have moderate impact on costs\n", "- **Gender and Region**: May have smaller but still significant effects\n", "\n", "#### 3. **Model Characteristics**\n", "- **Linear Regression**: Simple, interpretable, good baseline\n", "- **Ridge Regression**: Helps with regularization, reduces overfitting\n", "- **Lasso Regression**: Feature selection capability, sparse solutions\n", "- **Random Forest**: Captures non-linear relationships, handles interactions well\n", "- **Polynomial Regression**: Can capture non-linear patterns but may overfit\n", "\n", "#### 4. **Business Implications**\n", "- Smoking status is a critical factor in insurance pricing\n", "- Age-based pricing is justified by the data\n", "- BMI could be used for risk assessment\n", "- Regional differences may indicate varying healthcare costs\n", "\n", "#### 5. **Model Selection Recommendations**\n", "- For **production use**: Choose the model with best test R² and reasonable overfitting\n", "- For **interpretability**: Linear or Ridge regression\n", "- For **accuracy**: Random Forest or the best-performing model\n", "- Consider **ensemble methods** for potentially better performance\n", "\n", "### Next Steps\n", "1. **Hyperparameter tuning** for the best-performing models\n", "2. **Cross-validation** for more robust performance estimates\n", "3. **Feature engineering** to create new predictive features\n", "4. **Ensemble methods** combining multiple models\n", "5. **Model deployment** considerations for production use"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final summary statistics\n", "best_model_name = final_comparison.iloc[0]['Model']\n", "best_r2 = final_comparison.iloc[0]['R² (Test)']\n", "best_rmse = final_comparison.iloc[0]['RMSE']\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"REGRESSION ANALYSIS SUMMARY\")\n", "print(\"=\"*60)\n", "print(f\"Dataset: Insurance Cost Prediction\")\n", "print(f\"Total samples: {len(df)}\")\n", "print(f\"Features: {len(X.columns)}\")\n", "print(f\"Training samples: {len(X_train)}\")\n", "print(f\"Testing samples: {len(X_test)}\")\n", "print(f\"\\nBest performing model: {best_model_name}\")\n", "print(f\"Best R² score: {best_r2:.4f}\")\n", "print(f\"Best RMSE: ${best_rmse:.2f}\")\n", "print(f\"\\nTarget variable range: ${y.min():.2f} - ${y.max():.2f}\")\n", "print(f\"Target variable mean: ${y.mean():.2f}\")\n", "print(f\"Target variable std: ${y.std():.2f}\")\n", "print(\"=\"*60)\n", "print(\"Analysis completed successfully!\")\n", "print(\"=\"*60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}