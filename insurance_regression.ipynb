# Import libraries for data handling, visualization, and regression
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score

# Load the insurance dataset
file_path = 'Insurance_dataset.csv'  # Corrected path for same folder as notebook
df = pd.read_csv(file_path)
df.head()

# Basic EDA
print('Dataset Info:')
df.info()
print('\nSummary Statistics:')
display(df.describe())
print('\nMissing Values:')
print(df.isnull().sum())

# Encode categorical variables if present
df_encoded = pd.get_dummies(df, drop_first=True)
df_encoded.head()

# Define features and target
# Use the correct target column name based on the dataset
if 'Insurance_Cost' in df_encoded.columns:
    X = df_encoded.drop('Insurance_Cost', axis=1)
    y = df_encoded['Insurance_Cost']
    # Split into train and test sets
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    print(f'Training samples: {X_train.shape[0]}, Test samples: {X_test.shape[0]}')
else:
    print('Target column "Insurance_Cost" not found in df_encoded. Available columns:', df_encoded.columns.tolist())
    X_train = X_test = y_train = y_test = None

# Train Linear Regression model
if 'X_train' in locals() and 'y_train' in locals() and X_train is not None and y_train is not None:
    reg = LinearRegression()
    reg.fit(X_train, y_train)
    print('Model trained successfully.')
else:
    print('Training data not available. Please check previous steps for errors in data preparation.')

# Predict and evaluate
y_pred = reg.predict(X_test)
mse = mean_squared_error(y_test, y_pred)
r2 = r2_score(y_test, y_pred)
print(f'Mean Squared Error: {mse:.2f}')
print(f'R-squared: {r2:.2f}')

# Residual plot
residuals = y_test - y_pred
plt.figure(figsize=(8,5))
sns.histplot(residuals, kde=True)
plt.title('Residuals Distribution')
plt.xlabel('Residuals')
plt.show()

# Plot predictions vs actual values
plt.figure(figsize=(8,5))
plt.scatter(y_test, y_pred, alpha=0.6)
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')
plt.xlabel('Actual Charges')
plt.ylabel('Predicted Charges')
plt.title('Actual vs. Predicted Charges')
plt.show()