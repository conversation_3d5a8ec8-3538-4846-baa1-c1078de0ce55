#!/usr/bin/env python3
"""
Test script to check if all required dependencies are available
for the insurance regression analysis notebook.
"""

import sys

def test_imports():
    """Test if all required packages can be imported."""
    required_packages = [
        'pandas',
        'numpy',
        'matplotlib',
        'seaborn',
        'scipy',
        'sklearn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} - OK")
        except ImportError:
            print(f"✗ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("\nTo install missing packages, run:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    else:
        print("\n✓ All required packages are available!")
        return True

def check_dataset():
    """Check if the dataset file exists."""
    import os
    
    dataset_file = "Insurance_dataset.csv"
    if os.path.exists(dataset_file):
        print(f"✓ Dataset file '{dataset_file}' found")
        return True
    else:
        print(f"✗ Dataset file '{dataset_file}' not found")
        return False

def main():
    """Main function to run all tests."""
    print("Testing dependencies for Insurance Regression Analysis...")
    print("=" * 60)
    
    imports_ok = test_imports()
    dataset_ok = check_dataset()
    
    print("\n" + "=" * 60)
    if imports_ok and dataset_ok:
        print("✓ All checks passed! You can run the Jupyter notebook.")
        print("\nTo start the analysis, run:")
        print("jupyter notebook insurance_regression_analysis.ipynb")
    else:
        print("✗ Some checks failed. Please resolve the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
