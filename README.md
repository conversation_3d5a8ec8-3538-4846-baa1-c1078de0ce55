# Insurance Cost Regression Analysis

This repository contains a comprehensive regression analysis of insurance costs using machine learning techniques. The analysis predicts insurance costs based on demographic and health factors.

## 📊 Dataset Overview

The dataset (`Insurance_dataset.csv`) contains 1,002 records with the following features:

- **Age**: Age of the policyholder
- **BMI**: Body Mass Index
- **Smoker**: Whether the person is a smoker (yes/no)
- **Number_of_Children**: Number of children/dependents
- **Gender**: Gender of the policyholder (male/female)
- **Region**: Geographic region (northeast, northwest, southeast, southwest)
- **Insurance_Cost**: Annual insurance cost (target variable)

## 🔍 Analysis Components

### 1. Exploratory Data Analysis
- Data shape, types, and basic statistics
- Missing values analysis
- Distribution visualizations
- Correlation analysis
- Feature relationships exploration

### 2. Data Preprocessing
- Categorical variable encoding (one-hot encoding)
- Feature scaling for regularized models
- Train-test split (80/20)

### 3. Machine Learning Models
- **Linear Regression**: Baseline model
- **Ridge Regression**: L2 regularization
- **Lasso Regression**: L1 regularization with feature selection
- **Random Forest Regression**: Ensemble method for non-linear relationships
- **Polynomial Regression**: Captures non-linear patterns

### 4. Model Evaluation
- R² Score (coefficient of determination)
- RMSE (Root Mean Square Error)
- MAE (Mean Absolute Error)
- Overfitting analysis
- Cross-validation considerations

### 5. Visualizations
- Actual vs Predicted scatter plots
- Residual plots for model diagnostics
- Feature importance analysis
- Model performance comparisons

## 🚀 Getting Started

### Prerequisites

Make sure you have Python 3.7+ installed with the following packages:

```bash
pip install pandas numpy matplotlib seaborn scipy scikit-learn jupyter
```

### Running the Analysis

1. **Check Dependencies** (optional):
   ```bash
   python test_dependencies.py
   ```

2. **Start Jupyter Notebook**:
   ```bash
   jupyter notebook insurance_regression_analysis.ipynb
   ```

3. **Run All Cells**: Execute all cells in the notebook to perform the complete analysis.

## 📈 Key Findings

### Model Performance
The analysis compares multiple regression models to identify the best approach for predicting insurance costs.

### Feature Importance
- **Smoking Status**: Most significant predictor of insurance costs
- **Age**: Strong positive correlation with costs
- **BMI**: Important health indicator affecting costs
- **Number of Children**: Moderate impact on insurance costs
- **Gender and Region**: Smaller but measurable effects

### Business Insights
- Smoking status is the primary driver of insurance cost variations
- Age-based pricing is statistically justified
- BMI can be used for risk assessment and pricing
- Regional differences may reflect varying healthcare costs

## 📁 File Structure

```
├── Insurance_dataset.csv              # Dataset file
├── insurance_regression_analysis.ipynb # Main analysis notebook
├── test_dependencies.py              # Dependency checker script
└── README.md                         # This file
```

## 🔧 Technical Details

### Models Implemented
1. **Linear Regression**: Simple baseline with good interpretability
2. **Ridge Regression**: Regularized linear model to prevent overfitting
3. **Lasso Regression**: Feature selection through L1 regularization
4. **Random Forest**: Ensemble method handling non-linear relationships
5. **Polynomial Regression**: Captures polynomial relationships

### Evaluation Metrics
- **R² Score**: Proportion of variance explained by the model
- **RMSE**: Root Mean Square Error in dollars
- **MAE**: Mean Absolute Error in dollars
- **Overfitting Check**: Difference between train and test R²

## 📊 Expected Results

The analysis typically shows:
- R² scores ranging from 0.75 to 0.87 depending on the model
- RMSE values between $4,000 to $6,000
- Random Forest often performs best for this dataset
- Smoking status as the most important feature

## 🤝 Contributing

Feel free to fork this repository and submit pull requests for improvements:
- Additional models (XGBoost, Neural Networks)
- Advanced feature engineering
- Hyperparameter tuning
- Cross-validation implementation

## 📝 License

This project is open source and available under the MIT License.

## 📧 Contact

For questions or suggestions, please open an issue in this repository.

---

**Note**: This analysis is for educational and research purposes. For production insurance pricing, additional regulatory and business considerations would be required.
