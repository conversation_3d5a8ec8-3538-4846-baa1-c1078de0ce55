# Insurance Cost Linear Regression Analysis

This repository contains a comprehensive linear regression analysis of insurance costs. The analysis predicts insurance costs based on demographic and health factors using linear regression techniques.

## 📊 Dataset Overview

The dataset (`Insurance_dataset.csv`) contains 1,002 records with the following features:

- **Age**: Age of the policyholder
- **BMI**: Body Mass Index
- **Smoker**: Whether the person is a smoker (yes/no)
- **Number_of_Children**: Number of children/dependents
- **Gender**: Gender of the policyholder (male/female)
- **Region**: Geographic region (northeast, northwest, southeast, southwest)
- **Insurance_Cost**: Annual insurance cost (target variable)

## 🔍 Analysis Components

### 1. Exploratory Data Analysis
- Data shape, types, and basic statistics
- Missing values analysis
- Distribution visualizations
- Correlation analysis
- Feature relationships exploration

### 2. Data Preprocessing
- Categorical variable encoding (one-hot encoding)
- Feature scaling for regularized models
- Train-test split (80/20)

### 3. Linear Regression Model
- **Linear Regression**: Complete implementation and analysis
- **Coefficient Analysis**: Detailed interpretation of feature impacts
- **Model Assumptions**: Validation of linear regression assumptions
- **Cross-Validation**: Robust performance estimation

### 4. Model Evaluation
- R² Score (coefficient of determination)
- RMSE (Root Mean Square Error)
- MAE (Mean Absolute Error)
- Cross-validation analysis
- Model assumptions validation

### 5. Visualizations
- Actual vs Predicted scatter plots
- Residual plots for model diagnostics
- Coefficient importance visualization
- Q-Q plots for normality testing
- Residual distribution analysis

## 🚀 Getting Started

### Prerequisites

Make sure you have Python 3.7+ installed with the following packages:

```bash
pip install pandas numpy matplotlib seaborn scipy scikit-learn jupyter
```

### Running the Analysis

1. **Check Dependencies** (optional):
   ```bash
   python test_dependencies.py
   ```

2. **Start Jupyter Notebook**:
   ```bash
   jupyter notebook insurance_regression_analysis.ipynb
   ```

3. **Run All Cells**: Execute all cells in the notebook to perform the complete analysis.

## 📈 Key Findings

### Linear Regression Performance
The linear regression model provides excellent interpretability and solid predictive performance for insurance cost prediction.

### Feature Impact (Coefficients)
- **Smoking Status**: Largest coefficient impact on insurance costs
- **Age**: Positive coefficient showing costs increase with age
- **BMI**: Higher BMI associated with higher costs
- **Number of Children**: Variable impact depending on policy structure
- **Gender and Region**: Smaller but measurable coefficient effects

### Business Insights
- Linear relationships provide clear pricing rules
- Each feature's impact is quantifiable and interpretable
- Model coefficients can directly inform business decisions
- Transparent model suitable for regulated industries

## 📁 File Structure

```
├── Insurance_dataset.csv              # Dataset file
├── insurance_regression_analysis.ipynb # Main analysis notebook
├── test_dependencies.py              # Dependency checker script
└── README.md                         # This file
```

## 🔧 Technical Details

### Model Implemented
**Linear Regression**: Complete implementation with:
- Coefficient analysis and interpretation
- Model assumptions validation
- Cross-validation for robust performance estimation
- Comprehensive residual analysis
- Business-ready insights and recommendations

### Evaluation Metrics
- **R² Score**: Proportion of variance explained by the model
- **RMSE**: Root Mean Square Error in dollars
- **MAE**: Mean Absolute Error in dollars
- **Overfitting Check**: Difference between train and test R²

## 📊 Expected Results

The linear regression analysis typically shows:
- R² scores around 0.75-0.85 for insurance cost prediction
- RMSE values between $4,000 to $6,000
- Strong coefficient for smoking status
- Clear interpretable relationships between all features and costs

## ✨ Features of the Analysis

✅ **Complete data exploration** with multiple visualization types
✅ **Linear regression implementation** with detailed coefficient analysis
✅ **Proper data preprocessing** with categorical encoding
✅ **Multiple evaluation metrics** (R², RMSE, MAE)
✅ **Cross-validation analysis** for robust performance estimation
✅ **Coefficient interpretation** for clear business insights
✅ **Model assumptions validation** (linearity, normality, etc.)
✅ **Professional visualizations** with residual analysis
✅ **Detailed markdown explanations** for each step
✅ **Business-ready recommendations** and interpretations
✅ **Complete documentation** in README.md

The notebook is ready to run and will provide you with a comprehensive understanding of linear regression for insurance cost prediction, complete with detailed coefficient analysis and actionable business insights!

## 🤝 Contributing

Feel free to fork this repository and submit pull requests for improvements:
- Advanced feature engineering (interaction terms, polynomial features)
- Additional regression techniques (Ridge, Lasso, Polynomial)
- Enhanced residual analysis and diagnostics
- Statistical significance testing for coefficients
- Confidence intervals for predictions

## 📝 License

This project is open source and available under the MIT License.

## 📧 Contact

For questions or suggestions, please open an issue in this repository.

---

**Note**: This analysis is for educational and research purposes. For production insurance pricing, additional regulatory and business considerations would be required.
