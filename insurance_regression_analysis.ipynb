# Data manipulation and analysis
import pandas as pd
import numpy as np

# Data visualization
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

# Machine learning
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder, PolynomialFeatures
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Warnings
import warnings
warnings.filterwarnings('ignore')

# Set style for plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Display options
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)

print("Libraries imported successfully!")

# Load the dataset
df = pd.read_csv('Insurance_dataset.csv')

print("Dataset loaded successfully!")
print(f"Dataset shape: {df.shape}")
print("\nFirst 5 rows:")
df.head()

# Basic information about the dataset
print("Dataset Info:")
print(df.info())
print("\n" + "="*50)
print("\nBasic Statistics:")
df.describe()

# Check for missing values
print("Missing Values:")
missing_values = df.isnull().sum()
print(missing_values)
print(f"\nTotal missing values: {missing_values.sum()}")

# Check data types
print("\nData Types:")
print(df.dtypes)

# Check unique values for categorical columns
categorical_cols = ['Smoker', 'Gender', 'Region']
print("\nUnique values in categorical columns:")
for col in categorical_cols:
    print(f"{col}: {df[col].unique()}")

# Create a comprehensive visualization of the dataset
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
fig.suptitle('Insurance Dataset - Exploratory Data Analysis', fontsize=16, fontweight='bold')

# Distribution of Insurance Cost (target variable)
axes[0, 0].hist(df['Insurance_Cost'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
axes[0, 0].set_title('Distribution of Insurance Cost')
axes[0, 0].set_xlabel('Insurance Cost ($)')
axes[0, 0].set_ylabel('Frequency')

# Age distribution
axes[0, 1].hist(df['Age'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
axes[0, 1].set_title('Distribution of Age')
axes[0, 1].set_xlabel('Age')
axes[0, 1].set_ylabel('Frequency')

# BMI distribution
axes[0, 2].hist(df['BMI'], bins=20, alpha=0.7, color='salmon', edgecolor='black')
axes[0, 2].set_title('Distribution of BMI')
axes[0, 2].set_xlabel('BMI')
axes[0, 2].set_ylabel('Frequency')

# Smoker vs Insurance Cost
sns.boxplot(data=df, x='Smoker', y='Insurance_Cost', ax=axes[1, 0])
axes[1, 0].set_title('Insurance Cost by Smoking Status')
axes[1, 0].set_ylabel('Insurance Cost ($)')

# Gender vs Insurance Cost
sns.boxplot(data=df, x='Gender', y='Insurance_Cost', ax=axes[1, 1])
axes[1, 1].set_title('Insurance Cost by Gender')
axes[1, 1].set_ylabel('Insurance Cost ($)')

# Region vs Insurance Cost
sns.boxplot(data=df, x='Region', y='Insurance_Cost', ax=axes[1, 2])
axes[1, 2].set_title('Insurance Cost by Region')
axes[1, 2].set_ylabel('Insurance Cost ($)')
axes[1, 2].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

# Correlation analysis
# First, let's create a copy of the dataframe for correlation analysis
df_corr = df.copy()

# Encode categorical variables for correlation analysis
le_smoker = LabelEncoder()
le_gender = LabelEncoder()
le_region = LabelEncoder()

df_corr['Smoker_encoded'] = le_smoker.fit_transform(df_corr['Smoker'])
df_corr['Gender_encoded'] = le_gender.fit_transform(df_corr['Gender'])
df_corr['Region_encoded'] = le_region.fit_transform(df_corr['Region'])

# Select numerical columns for correlation
numerical_cols = ['Age', 'BMI', 'Number_of_Children', 'Smoker_encoded', 'Gender_encoded', 'Region_encoded', 'Insurance_Cost']
correlation_matrix = df_corr[numerical_cols].corr()

# Create correlation heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, 
            square=True, linewidths=0.5, cbar_kws={"shrink": .8})
plt.title('Correlation Matrix of Features', fontsize=14, fontweight='bold')
plt.tight_layout()
plt.show()

print("Correlation with Insurance Cost:")
correlations = correlation_matrix['Insurance_Cost'].sort_values(ascending=False)
for feature, corr in correlations.items():
    if feature != 'Insurance_Cost':
        print(f"{feature}: {corr:.3f}")

# Scatter plots to visualize relationships
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
fig.suptitle('Relationships between Features and Insurance Cost', fontsize=16, fontweight='bold')

# Age vs Insurance Cost
axes[0, 0].scatter(df['Age'], df['Insurance_Cost'], alpha=0.6, color='blue')
axes[0, 0].set_xlabel('Age')
axes[0, 0].set_ylabel('Insurance Cost ($)')
axes[0, 0].set_title('Age vs Insurance Cost')

# BMI vs Insurance Cost
axes[0, 1].scatter(df['BMI'], df['Insurance_Cost'], alpha=0.6, color='green')
axes[0, 1].set_xlabel('BMI')
axes[0, 1].set_ylabel('Insurance Cost ($)')
axes[0, 1].set_title('BMI vs Insurance Cost')

# Number of Children vs Insurance Cost
axes[1, 0].scatter(df['Number_of_Children'], df['Insurance_Cost'], alpha=0.6, color='red')
axes[1, 0].set_xlabel('Number of Children')
axes[1, 0].set_ylabel('Insurance Cost ($)')
axes[1, 0].set_title('Number of Children vs Insurance Cost')

# Age vs BMI colored by Smoker status
smokers = df[df['Smoker'] == 'yes']
non_smokers = df[df['Smoker'] == 'no']
axes[1, 1].scatter(non_smokers['Age'], non_smokers['BMI'], alpha=0.6, color='blue', label='Non-smoker')
axes[1, 1].scatter(smokers['Age'], smokers['BMI'], alpha=0.6, color='red', label='Smoker')
axes[1, 1].set_xlabel('Age')
axes[1, 1].set_ylabel('BMI')
axes[1, 1].set_title('Age vs BMI by Smoking Status')
axes[1, 1].legend()

plt.tight_layout()
plt.show()

# Create a copy of the dataset for preprocessing
df_processed = df.copy()

print("Original dataset shape:", df_processed.shape)
print("\nChecking for missing values:")
print(df_processed.isnull().sum())

# Since there are no missing values, we'll proceed with encoding categorical variables
print("\nNo missing values found. Proceeding with categorical encoding...")

# Encode categorical variables using one-hot encoding
# This is better than label encoding for categorical variables with no ordinal relationship

# One-hot encode categorical variables
df_encoded = pd.get_dummies(df_processed, columns=['Smoker', 'Gender', 'Region'], drop_first=True)

print("Dataset after one-hot encoding:")
print(f"Shape: {df_encoded.shape}")
print("\nColumns:")
print(df_encoded.columns.tolist())
print("\nFirst 5 rows:")
df_encoded.head()

# Prepare features and target variable
X = df_encoded.drop('Insurance_Cost', axis=1)
y = df_encoded['Insurance_Cost']

print("Features (X):")
print(f"Shape: {X.shape}")
print(f"Columns: {X.columns.tolist()}")

print("\nTarget variable (y):")
print(f"Shape: {y.shape}")
print(f"Statistics: Mean={y.mean():.2f}, Std={y.std():.2f}, Min={y.min():.2f}, Max={y.max():.2f}")

# Split the data into training and testing sets
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=None)

print("Data split completed:")
print(f"Training set: X_train {X_train.shape}, y_train {y_train.shape}")
print(f"Testing set: X_test {X_test.shape}, y_test {y_test.shape}")

# Feature scaling (important for regularized models)
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

print("\nFeature scaling completed.")
print(f"Scaled training features shape: {X_train_scaled.shape}")
print(f"Scaled testing features shape: {X_test_scaled.shape}")

# Initialize models
models = {
    'Linear Regression': LinearRegression(),
    'Ridge Regression': Ridge(alpha=1.0, random_state=42),
    'Lasso Regression': Lasso(alpha=1.0, random_state=42),
    'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42)
}

# Dictionary to store model results
model_results = {}

print("Models initialized:")
for name in models.keys():
    print(f"- {name}")

# Train models and make predictions
for name, model in models.items():
    print(f"\nTraining {name}...")
    
    # Use scaled data for linear models, original data for tree-based models
    if name in ['Ridge Regression', 'Lasso Regression']:
        model.fit(X_train_scaled, y_train)
        y_pred = model.predict(X_test_scaled)
        train_pred = model.predict(X_train_scaled)
    else:
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)
        train_pred = model.predict(X_train)
    
    # Calculate metrics
    r2_test = r2_score(y_test, y_pred)
    r2_train = r2_score(y_train, train_pred)
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    mae = mean_absolute_error(y_test, y_pred)
    
    # Store results
    model_results[name] = {
        'model': model,
        'y_pred': y_pred,
        'train_pred': train_pred,
        'r2_test': r2_test,
        'r2_train': r2_train,
        'rmse': rmse,
        'mae': mae
    }
    
    print(f"  R² (Test): {r2_test:.4f}")
    print(f"  R² (Train): {r2_train:.4f}")
    print(f"  RMSE: {rmse:.2f}")
    print(f"  MAE: {mae:.2f}")

print("\nAll models trained successfully!")

# Create a comparison table of model performances
comparison_df = pd.DataFrame({
    'Model': list(model_results.keys()),
    'R² (Test)': [results['r2_test'] for results in model_results.values()],
    'R² (Train)': [results['r2_train'] for results in model_results.values()],
    'RMSE': [results['rmse'] for results in model_results.values()],
    'MAE': [results['mae'] for results in model_results.values()]
})

# Sort by R² (Test) in descending order
comparison_df = comparison_df.sort_values('R² (Test)', ascending=False)

print("Model Performance Comparison:")
print("=" * 80)
print(comparison_df.to_string(index=False, float_format='%.4f'))

# Calculate overfitting indicator (difference between train and test R²)
comparison_df['Overfitting'] = comparison_df['R² (Train)'] - comparison_df['R² (Test)']
print("\nOverfitting Analysis (Train R² - Test R²):")
print("=" * 50)
for idx, row in comparison_df.iterrows():
    print(f"{row['Model']}: {row['Overfitting']:.4f}")

# Create actual vs predicted plots for all models
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('Actual vs Predicted Values - All Models', fontsize=16, fontweight='bold')

axes = axes.ravel()

for i, (name, results) in enumerate(model_results.items()):
    ax = axes[i]
    
    # Plot actual vs predicted
    ax.scatter(y_test, results['y_pred'], alpha=0.6, color='blue')
    
    # Plot perfect prediction line
    min_val = min(y_test.min(), results['y_pred'].min())
    max_val = max(y_test.max(), results['y_pred'].max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='Perfect Prediction')
    
    ax.set_xlabel('Actual Insurance Cost ($)')
    ax.set_ylabel('Predicted Insurance Cost ($)')
    ax.set_title(f'{name}\nR² = {results["r2_test"]:.4f}, RMSE = {results["rmse"]:.2f}')
    ax.legend()
    ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Create residual plots for all models
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('Residual Plots - All Models', fontsize=16, fontweight='bold')

axes = axes.ravel()

for i, (name, results) in enumerate(model_results.items()):
    ax = axes[i]
    
    # Calculate residuals
    residuals = y_test - results['y_pred']
    
    # Plot residuals vs predicted values
    ax.scatter(results['y_pred'], residuals, alpha=0.6, color='green')
    ax.axhline(y=0, color='red', linestyle='--', lw=2)
    
    ax.set_xlabel('Predicted Insurance Cost ($)')
    ax.set_ylabel('Residuals ($)')
    ax.set_title(f'{name} - Residuals')
    ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Feature importance analysis (for Random Forest)
rf_model = model_results['Random Forest']['model']
feature_importance = pd.DataFrame({
    'Feature': X.columns,
    'Importance': rf_model.feature_importances_
}).sort_values('Importance', ascending=False)

plt.figure(figsize=(10, 6))
sns.barplot(data=feature_importance, x='Importance', y='Feature', palette='viridis')
plt.title('Feature Importance - Random Forest Model', fontsize=14, fontweight='bold')
plt.xlabel('Importance Score')
plt.tight_layout()
plt.show()

print("Feature Importance (Random Forest):")
print("=" * 40)
for idx, row in feature_importance.iterrows():
    print(f"{row['Feature']}: {row['Importance']:.4f}")

# Implement polynomial regression for comparison
poly_features = PolynomialFeatures(degree=2, include_bias=False)
X_train_poly = poly_features.fit_transform(X_train)
X_test_poly = poly_features.transform(X_test)

# Train polynomial regression
poly_model = LinearRegression()
poly_model.fit(X_train_poly, y_train)

# Make predictions
y_pred_poly = poly_model.predict(X_test_poly)
y_train_pred_poly = poly_model.predict(X_train_poly)

# Calculate metrics
r2_test_poly = r2_score(y_test, y_pred_poly)
r2_train_poly = r2_score(y_train, y_train_pred_poly)
rmse_poly = np.sqrt(mean_squared_error(y_test, y_pred_poly))
mae_poly = mean_absolute_error(y_test, y_pred_poly)

print("Polynomial Regression (Degree 2) Results:")
print(f"R² (Test): {r2_test_poly:.4f}")
print(f"R² (Train): {r2_train_poly:.4f}")
print(f"RMSE: {rmse_poly:.2f}")
print(f"MAE: {mae_poly:.2f}")
print(f"Overfitting: {r2_train_poly - r2_test_poly:.4f}")

# Add to model results for comparison
model_results['Polynomial Regression'] = {
    'model': poly_model,
    'y_pred': y_pred_poly,
    'train_pred': y_train_pred_poly,
    'r2_test': r2_test_poly,
    'r2_train': r2_train_poly,
    'rmse': rmse_poly,
    'mae': mae_poly
}

# Updated comparison including polynomial regression
final_comparison = pd.DataFrame({
    'Model': list(model_results.keys()),
    'R² (Test)': [results['r2_test'] for results in model_results.values()],
    'R² (Train)': [results['r2_train'] for results in model_results.values()],
    'RMSE': [results['rmse'] for results in model_results.values()],
    'MAE': [results['mae'] for results in model_results.values()]
})

final_comparison['Overfitting'] = final_comparison['R² (Train)'] - final_comparison['R² (Test)']
final_comparison = final_comparison.sort_values('R² (Test)', ascending=False)

print("FINAL MODEL PERFORMANCE COMPARISON:")
print("=" * 90)
print(final_comparison.to_string(index=False, float_format='%.4f'))

# Visualize model comparison
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

# R² comparison
x_pos = np.arange(len(final_comparison))
ax1.bar(x_pos - 0.2, final_comparison['R² (Train)'], 0.4, label='Train R²', alpha=0.8)
ax1.bar(x_pos + 0.2, final_comparison['R² (Test)'], 0.4, label='Test R²', alpha=0.8)
ax1.set_xlabel('Models')
ax1.set_ylabel('R² Score')
ax1.set_title('R² Score Comparison')
ax1.set_xticks(x_pos)
ax1.set_xticklabels(final_comparison['Model'], rotation=45)
ax1.legend()
ax1.grid(True, alpha=0.3)

# RMSE comparison
ax2.bar(final_comparison['Model'], final_comparison['RMSE'], alpha=0.8, color='coral')
ax2.set_xlabel('Models')
ax2.set_ylabel('RMSE ($)')
ax2.set_title('RMSE Comparison')
ax2.tick_params(axis='x', rotation=45)
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Final summary statistics
best_model_name = final_comparison.iloc[0]['Model']
best_r2 = final_comparison.iloc[0]['R² (Test)']
best_rmse = final_comparison.iloc[0]['RMSE']

print("\n" + "="*60)
print("REGRESSION ANALYSIS SUMMARY")
print("="*60)
print(f"Dataset: Insurance Cost Prediction")
print(f"Total samples: {len(df)}")
print(f"Features: {len(X.columns)}")
print(f"Training samples: {len(X_train)}")
print(f"Testing samples: {len(X_test)}")
print(f"\nBest performing model: {best_model_name}")
print(f"Best R² score: {best_r2:.4f}")
print(f"Best RMSE: ${best_rmse:.2f}")
print(f"\nTarget variable range: ${y.min():.2f} - ${y.max():.2f}")
print(f"Target variable mean: ${y.mean():.2f}")
print(f"Target variable std: ${y.std():.2f}")
print("="*60)
print("Analysis completed successfully!")
print("="*60)